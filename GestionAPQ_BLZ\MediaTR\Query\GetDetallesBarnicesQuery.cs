namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.DatoLita01;

public record GetDetallesBarnicesQuery(string? IdBarniz, bool SacarViscosidades)
    : IRequest<ListResult<BarnizDTO>>;

public class GetDetallesBarnicesQueryHandler : IRequestHandler<GetDetallesBarnicesQuery, ListResult<BarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;
    private readonly IAViscosRepo _aViscosRepo;

    public GetDetallesBarnicesQueryHandler(IAArticuRepo aArticuRepo, IAViscosRepo aViscosRepo)
    {
        _aArticuRepo = aArticuRepo;
        _aViscosRepo = aViscosRepo;
    }

    public async Task<ListResult<BarnizDTO>> Handle(GetDetallesBarnicesQuery request,
        CancellationToken cancellationToken)
    {
        var data = request.SacarViscosidades
            ? await GetBarnicesConViscosidades(request.IdBarniz, cancellationToken)
            : await GetBarnicesSinViscosidades(request.IdBarniz, cancellationToken);

        return new ListResult<BarnizDTO>
        {
            Data = data,
            Errors = []
        };
    }

    private async Task<List<BarnizDTO>> GetBarnicesConViscosidades(string? idBarniz,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await _aArticuRepo.GetBarnicesLeftJoinViscosidades(
            idBarniz,
            _aViscosRepo,
            true,
            cancellationToken);

        return listaBarnices
            .GroupBy(x => x.Item1.Codigo)
            .Select(g => new BarnizDTO
            {
                Articulo = TinyMapper.Map<AArticuDTO>(g.First().Item1),
                ListaViscosidades = g.Where(x => x.Item2 != null)
                    .Select(x => TinyMapper.Map<AViscosDTO>(x.Item2))
                    .ToList()
            }).ToList();
    }

    private async Task<List<BarnizDTO>> GetBarnicesSinViscosidades(string? idBarniz,
        CancellationToken cancellationToken)
    {
        var listaBarnices = await _aArticuRepo.GetBarnices(idBarniz, true, cancellationToken);

        return listaBarnices.Select(barniz => new BarnizDTO
        {
            Codigo = idBarniz,
            Articulo = TinyMapper.Map<AArticuDTO>(barniz)
        }).ToList();
    }
}